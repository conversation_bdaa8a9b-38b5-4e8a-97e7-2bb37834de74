<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL解码测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin: 15px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .input { background: #f8f9fa; padding: 8px; border-radius: 3px; word-break: break-all; margin: 5px 0; }
        .output { background: #d4edda; padding: 8px; border-radius: 3px; margin: 5px 0; }
        .error { background: #f8d7da; }
        .improvement { background: #fff3cd; padding: 8px; border-radius: 3px; margin: 5px 0; }
        .before-after { display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin: 10px 0; }
        .before, .after { padding: 10px; border-radius: 5px; }
        .before { background: #f8d7da; }
        .after { background: #d4edda; }
    </style>
</head>
<body>
    <h1>URL解码功能测试</h1>
    
    <div id="test-results"></div>

    <script>
        // 更新后的 getFileName 函数
        function getFileName(path) {
            try {
                let fileName = '';
                
                // 如果是完整URL，先解析URL获取pathname
                if (path.startsWith('http')) {
                    const url = new URL(path)
                    const pathname = url.pathname
                    const parts = pathname.split('/')
                    fileName = parts[parts.length - 1]
                } else {
                    // 如果是相对路径，直接分割
                    const parts = path.split('/')
                    fileName = parts[parts.length - 1]
                }
                
                // 解码URL编码的文件名（如 %20 -> 空格）
                try {
                    fileName = decodeURIComponent(fileName)
                } catch (decodeError) {
                    // 如果解码失败，使用原始文件名
                    console.warn('Failed to decode filename:', fileName, decodeError)
                }
                
                return fileName
            } catch (error) {
                // 如果URL解析失败，回退到简单分割
                const parts = path.split('/')
                let fileName = parts[parts.length - 1].split('?')[0] // 移除查询参数
                
                // 尝试解码
                try {
                    fileName = decodeURIComponent(fileName)
                } catch (decodeError) {
                    // 解码失败时保持原样
                }
                
                return fileName
            }
        }

        // 旧版本的 getFileName 函数（用于对比）
        function getFileNameOld(path) {
            try {
                if (path.startsWith('http')) {
                    const url = new URL(path)
                    const pathname = url.pathname
                    const parts = pathname.split('/')
                    return parts[parts.length - 1]
                }
                const parts = path.split('/')
                return parts[parts.length - 1]
            } catch (error) {
                const parts = path.split('/')
                return parts[parts.length - 1].split('?')[0]
            }
        }

        // 测试用例
        const testCases = [
            {
                name: "Tap-Talk音频文件（URL编码）",
                input: "https://moodplay-test.oss-cn-hangzhou.aliyuncs.com/assets/pages/tap-talk/night/SpotiMate.io%20-%20Unfolding%20-%204%20AM%20Edit%20-%20Manolakas.mp3?OSSAccessKeyId=LTAI5tA4QUxbD1uHN5YaM8gM&Expires=1753869024&Signature=7p9Rd9PfnbjTij98A63eNMssiQI%3D",
                expected: "SpotiMate.io - Unfolding - 4 AM Edit - Manolakas.mp3"
            },
            {
                name: "中文文件名（URL编码）",
                input: "assets/pages/end-goal/%E6%9D%BE%E5%BC%9B%E6%97%B6%E5%88%BB/tag-icon.svg",
                expected: "tag-icon.svg"
            },
            {
                name: "带空格的文件名",
                input: "https://example.com/My%20Audio%20File.mp3",
                expected: "My Audio File.mp3"
            },
            {
                name: "特殊字符文件名",
                input: "assets/files/Test%21%40%23%24%25.txt",
                expected: "Test!@#$%.txt"
            },
            {
                name: "正常文件名（无编码）",
                input: "assets/pages/homepage/background-video.mp4",
                expected: "background-video.mp4"
            },
            {
                name: "带版本参数的编码文件名",
                input: "assets/pages/test/My%20File%20Name.png?v=202506302210",
                expected: "My File Name.png"
            }
        ];

        // 运行测试
        const resultsDiv = document.getElementById('test-results');
        
        testCases.forEach((testCase, index) => {
            const oldResult = getFileNameOld(testCase.input);
            const newResult = getFileName(testCase.input);
            const isCorrect = newResult === testCase.expected;
            const isImproved = oldResult !== newResult;
            
            resultsDiv.innerHTML += `
                <div class="test-case">
                    <h3>测试 ${index + 1}: ${testCase.name}</h3>
                    <div class="input"><strong>输入:</strong> ${testCase.input}</div>
                    
                    <div class="before-after">
                        <div class="before">
                            <strong>优化前:</strong><br>
                            ${oldResult}
                        </div>
                        <div class="after">
                            <strong>优化后:</strong><br>
                            ${newResult}
                        </div>
                    </div>
                    
                    <div class="output ${isCorrect ? '' : 'error'}">
                        <strong>期望结果:</strong> ${testCase.expected}<br>
                        <strong>测试结果:</strong> ${isCorrect ? '✅ 通过' : '❌ 失败'}<br>
                        <strong>是否改进:</strong> ${isImproved ? '✅ 有改进' : '⚪ 无变化'}
                    </div>
                    
                    ${isImproved ? `
                        <div class="improvement">
                            <strong>🎉 改进效果:</strong> 文件名从 "${oldResult}" 优化为 "${newResult}"
                        </div>
                    ` : ''}
                </div>
            `;
        });

        // 添加总结
        resultsDiv.innerHTML += `
            <div class="test-case" style="background: #e3f2fd; border-color: #2196f3;">
                <h3>🎯 优化总结</h3>
                <ul>
                    <li><strong>URL解码:</strong> 自动将 %20 转换为空格，%21 转换为 ! 等</li>
                    <li><strong>中文支持:</strong> 正确处理URL编码的中文字符</li>
                    <li><strong>特殊字符:</strong> 支持各种特殊字符的解码</li>
                    <li><strong>向后兼容:</strong> 对于无需解码的文件名保持原样</li>
                    <li><strong>错误处理:</strong> 解码失败时回退到原始文件名</li>
                </ul>
                <p><strong>主要改进:</strong> "SpotiMate.io%20-%20Unfolding%20-%204%20AM%20Edit%20-%20Manolakas.mp3" 
                → "SpotiMate.io - Unfolding - 4 AM Edit - Manolakas.mp3"</p>
            </div>
        `;
    </script>
</body>
</html>
