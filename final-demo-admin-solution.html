<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin CDN绕过 + 文件名优化 - 完整解决方案演示</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 1200px; margin: 0 auto; }
        .hero { background: white; border-radius: 12px; padding: 30px; margin-bottom: 20px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); }
        .demo-section { background: white; margin: 20px 0; padding: 25px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.1); }
        .problem-solution { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .problem, .solution { padding: 20px; border-radius: 8px; }
        .problem { background: #ffebee; border-left: 4px solid #f44336; }
        .solution { background: #e8f5e8; border-left: 4px solid #4caf50; }
        .resource-item { display: flex; align-items: center; padding: 15px; margin: 10px 0; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff; }
        .resource-icon { width: 40px; height: 40px; background: linear-gradient(135deg, #007bff, #0056b3); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-right: 15px; color: white; font-weight: bold; font-size: 18px; }
        .resource-info { flex: 1; }
        .resource-name { font-weight: 600; color: #333; margin-bottom: 8px; font-size: 16px; }
        .resource-url { font-size: 12px; color: #666; word-break: break-all; background: #fff; padding: 8px; border-radius: 4px; }
        .improvement-badge { background: #4caf50; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-left: 10px; }
        .success { color: #28a745; font-weight: bold; }
        .login-section { background: linear-gradient(135deg, #e3f2fd, #bbdefb); border-left: 4px solid #2196f3; }
        button { background: linear-gradient(135deg, #007bff, #0056b3); color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; margin: 8px; font-weight: 500; transition: all 0.3s; }
        button:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,123,255,0.3); }
        button:disabled { background: #ccc; cursor: not-allowed; transform: none; box-shadow: none; }
        .loading { color: #666; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .stat-number { font-size: 32px; font-weight: bold; margin-bottom: 5px; }
        .stat-label { font-size: 14px; opacity: 0.9; }
    </style>
</head>
<body>
    <div class="container">
        <div class="hero">
            <h1>🎉 Admin CDN绕过 + 文件名优化解决方案</h1>
            <p>完整解决CDN User-Agent限制问题，同时优化文件名显示体验</p>
            
            <div class="problem-solution">
                <div class="problem">
                    <h3>❌ 原始问题</h3>
                    <ul>
                        <li>CDN限制admin访问</li>
                        <li>显示完整签名URL</li>
                        <li>URL编码影响可读性</li>
                    </ul>
                </div>
                <div class="solution">
                    <h3>✅ 解决方案</h3>
                    <ul>
                        <li>admin专用签名URL端点</li>
                        <li>智能文件名提取</li>
                        <li>自动URL解码</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="demo-section login-section">
            <h3>🔐 步骤1: 管理员登录</h3>
            <button onclick="login()" id="loginBtn">登录管理员账户</button>
            <div id="loginStatus"></div>
        </div>

        <div class="demo-section">
            <h3>📊 步骤2: 加载配置数据</h3>
            <button onclick="loadConfigs()" id="loadBtn" disabled>加载所有配置</button>
            <div id="statsContainer"></div>
            <div id="configResults"></div>
        </div>

        <div class="demo-section">
            <h3>🎯 核心改进对比</h3>
            <div class="problem-solution">
                <div class="problem">
                    <h4>优化前</h4>
                    <code style="word-break: break-all; font-size: 12px;">
                        SpotiMate.io%20-%20Unfolding%20-%204%20AM%20Edit%20-%20Manolakas.mp3
                    </code>
                </div>
                <div class="solution">
                    <h4>优化后</h4>
                    <code style="font-size: 14px;">
                        SpotiMate.io - Unfolding - 4 AM Edit - Manolakas.mp3
                    </code>
                </div>
            </div>
        </div>
    </div>

    <script>
        let adminToken = '';
        const API_BASE = 'http://localhost:3000/api';

        // 更新后的文件名提取函数（包含URL解码）
        function getFileName(path) {
            try {
                let fileName = '';
                
                if (path.startsWith('http')) {
                    const url = new URL(path)
                    const pathname = url.pathname
                    const parts = pathname.split('/')
                    fileName = parts[parts.length - 1]
                } else {
                    const parts = path.split('/')
                    fileName = parts[parts.length - 1]
                }
                
                // 解码URL编码的文件名
                try {
                    fileName = decodeURIComponent(fileName)
                } catch (decodeError) {
                    console.warn('Failed to decode filename:', fileName, decodeError)
                }
                
                return fileName
            } catch (error) {
                const parts = path.split('/')
                let fileName = parts[parts.length - 1].split('?')[0]
                
                try {
                    fileName = decodeURIComponent(fileName)
                } catch (decodeError) {
                    // 解码失败时保持原样
                }
                
                return fileName
            }
        }

        async function login() {
            const loginBtn = document.getElementById('loginBtn');
            const loginStatus = document.getElementById('loginStatus');
            
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            
            try {
                const response = await fetch(`${API_BASE}/admin/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'm$i4un5ggi9#4Dg3'
                    })
                });
                
                const data = await response.json();
                if (data.code === 0) {
                    adminToken = data.data.token;
                    loginStatus.innerHTML = `<div class="success">✅ 登录成功！准备加载配置数据</div>`;
                    document.getElementById('loadBtn').disabled = false;
                } else {
                    loginStatus.innerHTML = `<div style="color: red;">❌ 登录失败: ${data.msg}</div>`;
                }
            } catch (error) {
                loginStatus.innerHTML = `<div style="color: red;">❌ 登录错误: ${error.message}</div>`;
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = '重新登录';
            }
        }

        async function loadConfigs() {
            const loadBtn = document.getElementById('loadBtn');
            const resultsDiv = document.getElementById('configResults');
            const statsContainer = document.getElementById('statsContainer');
            
            loadBtn.disabled = true;
            loadBtn.textContent = '加载中...';
            resultsDiv.innerHTML = '<div class="loading">正在加载配置数据...</div>';
            
            const endpoints = ['homepage', 'onboarding', 'end-goal', 'tap-talk'];
            let allResources = [];
            let totalFiles = 0;
            let urlEncodedFiles = 0;
            
            try {
                for (const endpoint of endpoints) {
                    const response = await fetch(`${API_BASE}/admin/admin-config/${endpoint}`, {
                        headers: { 'Authorization': `Bearer ${adminToken}` }
                    });
                    
                    const data = await response.json();
                    if (data.code === 0) {
                        const resources = extractResources(data.data, endpoint);
                        allResources = allResources.concat(resources);
                        totalFiles += resources.length;
                        urlEncodedFiles += resources.filter(r => r.originalFileName !== r.decodedFileName).length;
                    }
                }
                
                // 显示统计信息
                statsContainer.innerHTML = `
                    <div class="stats">
                        <div class="stat-card">
                            <div class="stat-number">${totalFiles}</div>
                            <div class="stat-label">总文件数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${urlEncodedFiles}</div>
                            <div class="stat-label">需要解码的文件</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">100%</div>
                            <div class="stat-label">使用签名URL</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${endpoints.length}</div>
                            <div class="stat-label">配置页面</div>
                        </div>
                    </div>
                `;
                
                displayResources(allResources);
                
            } catch (error) {
                resultsDiv.innerHTML = `<div style="color: red;">❌ 加载失败: ${error.message}</div>`;
            } finally {
                loadBtn.disabled = false;
                loadBtn.textContent = '重新加载';
            }
        }

        function extractResources(data, endpoint) {
            const resources = [];
            
            function findUrls(obj, path = '', category = endpoint) {
                for (const [key, value] of Object.entries(obj)) {
                    const currentPath = path ? `${path}.${key}` : key;
                    if (typeof value === 'string' && value.includes('aliyuncs.com')) {
                        const originalFileName = value.split('/').pop().split('?')[0];
                        const decodedFileName = getFileName(value);
                        
                        resources.push({
                            category,
                            field: currentPath,
                            url: value,
                            originalFileName,
                            decodedFileName,
                            isImproved: originalFileName !== decodedFileName
                        });
                    } else if (typeof value === 'object' && value !== null) {
                        findUrls(value, currentPath, category);
                    }
                }
            }
            
            findUrls(data);
            return resources;
        }

        function displayResources(resources) {
            const resultsDiv = document.getElementById('configResults');
            
            if (resources.length === 0) {
                resultsDiv.innerHTML = '<div>没有找到资源文件</div>';
                return;
            }
            
            // 按类别分组
            const groupedResources = resources.reduce((groups, resource) => {
                if (!groups[resource.category]) {
                    groups[resource.category] = [];
                }
                groups[resource.category].push(resource);
                return groups;
            }, {});
            
            let html = `<h4>📁 资源文件详情 (共 ${resources.length} 个文件)</h4>`;
            
            for (const [category, categoryResources] of Object.entries(groupedResources)) {
                const improvedCount = categoryResources.filter(r => r.isImproved).length;
                
                html += `<h5>${category.toUpperCase()} - ${categoryResources.length} 个文件 ${improvedCount > 0 ? `(${improvedCount} 个已优化)` : ''}</h5>`;
                
                // 显示前几个文件作为示例
                categoryResources.slice(0, 3).forEach(resource => {
                    const fileType = resource.decodedFileName.split('.').pop().toLowerCase();
                    const icon = fileType === 'svg' || fileType === 'png' || fileType === 'jpg' || fileType === 'jpeg' ? '🖼️' : 
                                fileType === 'mp3' || fileType === 'wav' ? '🎵' : 
                                fileType === 'mp4' ? '🎬' : '📄';
                    
                    html += `
                        <div class="resource-item">
                            <div class="resource-icon">${icon}</div>
                            <div class="resource-info">
                                <div class="resource-name">
                                    ${resource.decodedFileName}
                                    ${resource.isImproved ? '<span class="improvement-badge">已优化</span>' : ''}
                                </div>
                                <div class="resource-url">
                                    <strong>完整签名URL:</strong> ${resource.url.substring(0, 100)}...
                                </div>
                                ${resource.isImproved ? `
                                    <div style="margin-top: 8px; font-size: 12px;">
                                        <strong>原始:</strong> <code>${resource.originalFileName}</code><br>
                                        <strong>优化:</strong> <code>${resource.decodedFileName}</code>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    `;
                });
                
                if (categoryResources.length > 3) {
                    html += `<div style="color: #666; font-style: italic; margin: 10px 0;">...还有 ${categoryResources.length - 3} 个文件</div>`;
                }
            }
            
            resultsDiv.innerHTML = html;
        }
    </script>
</body>
</html>
