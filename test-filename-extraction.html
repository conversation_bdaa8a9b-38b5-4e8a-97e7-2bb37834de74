<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件名提取测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin: 15px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .input { background: #f8f9fa; padding: 5px; border-radius: 3px; word-break: break-all; }
        .output { background: #d4edda; padding: 5px; border-radius: 3px; margin-top: 5px; }
        .error { background: #f8d7da; }
    </style>
</head>
<body>
    <h1>文件名提取功能测试</h1>
    
    <div id="test-results"></div>

    <script>
        // 复制我们的 getFileName 函数
        function getFileName(path) {
            try {
                // 如果是完整URL，先解析URL获取pathname
                if (path.startsWith('http')) {
                    const url = new URL(path)
                    const pathname = url.pathname
                    const parts = pathname.split('/')
                    return parts[parts.length - 1]
                }
                // 如果是相对路径，直接分割
                const parts = path.split('/')
                return parts[parts.length - 1]
            } catch (error) {
                // 如果URL解析失败，回退到简单分割
                const parts = path.split('/')
                return parts[parts.length - 1].split('?')[0] // 移除查询参数
            }
        }

        // 测试用例
        const testCases = [
            {
                name: "OSS签名URL",
                input: "https://moodplay-test.oss-cn-hangzhou.aliyuncs.com/assets/pages/tap-talk/audio-paused.svg?OSSAccessKeyId=LTAI5tA4QUxbD1uHN5YaM8gM&Expires=1753869361&Signature=H%2FKyQulscmxYIfHxRWWZ7kJ2Mps%3D",
                expected: "audio-paused.svg"
            },
            {
                name: "相对路径",
                input: "assets/pages/onboarding/step1-image.png",
                expected: "step1-image.png"
            },
            {
                name: "简单文件名",
                input: "background-video.mp4",
                expected: "background-video.mp4"
            },
            {
                name: "带版本参数的路径",
                input: "assets/pages/homepage/background-video.mp4?v=202506302210",
                expected: "background-video.mp4"
            },
            {
                name: "CDN URL",
                input: "https://cdn.moodplay.top/assets/pages/homepage/logo.png",
                expected: "logo.png"
            }
        ];

        // 运行测试
        const resultsDiv = document.getElementById('test-results');
        
        testCases.forEach((testCase, index) => {
            const result = getFileName(testCase.input);
            const isCorrect = result === testCase.expected;
            
            resultsDiv.innerHTML += `
                <div class="test-case">
                    <h3>测试 ${index + 1}: ${testCase.name}</h3>
                    <div class="input"><strong>输入:</strong> ${testCase.input}</div>
                    <div class="output ${isCorrect ? '' : 'error'}">
                        <strong>输出:</strong> ${result}<br>
                        <strong>期望:</strong> ${testCase.expected}<br>
                        <strong>结果:</strong> ${isCorrect ? '✅ 通过' : '❌ 失败'}
                    </div>
                </div>
            `;
        });
    </script>
</body>
</html>
