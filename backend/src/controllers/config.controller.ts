import { Request, Response, NextFunction } from 'express';
import { OssService } from '../services/oss.service';
import { BridgeConfig, CheckInConfig, DivinationConfig, EditPageConfig, EndGoalConfig, ExploreConfig, HomepageConfig, OnboardingConfig, TapTalkConfig } from '../types/config.type';
import logger from '../utils/logger';
import { MyConfig, PdfConfig } from '../types/my.type';

export class ConfigController {

  static async homepage(req: Request, res: Response, next: NextFunction) {
    try {
      const config = await OssService.getObjectJsonContentWithCache<HomepageConfig>('homepage');

      config.backgroundVideo = OssService.generateResourceUrl(config.backgroundVideo);
      config.guideButtonIcon = OssService.generateResourceUrl(config.guideButtonIcon);
      config.guideTextImage = OssService.generateResourceUrl(config.guideTextImage);
      config.closeIcon = OssService.generateResourceUrl(config.closeIcon);

      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      next(error);
    }
  }

  static async editpage(req: Request, res: Response, next: NextFunction) {
    try {
      const config = await OssService.getObjectJsonContentWithCache<EditPageConfig>('edit-page');

      config.speakingGif = OssService.generateResourceUrl(config.speakingGif);
      config.keyboardIcon = OssService.generateResourceUrl(config.keyboardIcon);
      config.voiceIcon = OssService.generateResourceUrl(config.voiceIcon);
      config.confirmArrowIcon = OssService.generateResourceUrl(config.confirmArrowIcon);
      config.bottomMask = OssService.generateResourceUrl(config.bottomMask);

      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      next(error);
    }
  }

  static async checkIn(req: Request, res: Response, next: NextFunction) {
    try {
      const config = await OssService.getObjectJsonContentWithCache<CheckInConfig>('check-in');

      config.checkedIcon = OssService.generateResourceUrl(config.checkedIcon);
      config.uncheckedIcon = OssService.generateResourceUrl(config.uncheckedIcon);
      config.emotionBackgroundImage = OssService.generateResourceUrl(config.emotionBackgroundImage);
      config.unSelectedEmotionBackgroundImage = OssService.generateResourceUrl(config.unSelectedEmotionBackgroundImage);

      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      next(error);
    }
  }

  static async endGoal(req: Request, res: Response, next: NextFunction) {
    try {
      const config = await OssService.getObjectJsonContentWithCache<EndGoalConfig>('end-goal');

      config.maskImage = OssService.generateResourceUrl(config.maskImage);
      config.infoIcon = OssService.generateResourceUrl(config.infoIcon);


      for (const goalConfig of config.goalConfigs) {
        const images = await OssService.getAllObjectNames(`assets/pages/end-goal/${goalConfig.goalKey}/images/`)
        logger.info(`images: ${images}`);

        // 第一步：根据文件名分组
        const backgroundImages: string[] = []; // N0X格式的图片，用于背景
        const cardBackgroundImages: string[] = []; // 0N0X格式的图片，用于卡片

        for (const imagePath of images) {
          const lastSlashIndex = imagePath.lastIndexOf('/');
          const fileName = imagePath.substring(lastSlashIndex + 1);

          // 判断是否以0开头
          if (fileName.startsWith('0') && fileName.length > 1 && /^\d/.test(fileName.charAt(1))) {
            // 0N0X格式，用于卡片
            cardBackgroundImages.push(imagePath);
          } else {
            // N0X格式，用于背景
            backgroundImages.push(imagePath);
          }
        }

        // 第二步：从背景图片中随机选择一张
        if (backgroundImages.length > 0) {
          const randomIndex = Math.floor(Math.random() * backgroundImages.length);
          const selectedBackgroundImage = backgroundImages[randomIndex];

          // 第三步：处理选中的背景图片
          goalConfig.displayBackgroundImageKey = selectedBackgroundImage;

          // 找到对应的卡片版本
          const lastSlashIndex = selectedBackgroundImage.lastIndexOf('/');
          const pathPart = selectedBackgroundImage.substring(0, lastSlashIndex + 1);
          const fileName = selectedBackgroundImage.substring(lastSlashIndex + 1);

          // 提取文件名（不包含扩展名）
          const dotIndex = fileName.lastIndexOf('.');
          const fileNameWithoutExt = dotIndex > 0 ? fileName.substring(0, dotIndex) : fileName;

          // 在卡片图片中找到对应的版本
          const matchingCardImage = cardBackgroundImages.find(cardImagePath => {
            const cardLastSlashIndex = cardImagePath.lastIndexOf('/');
            const cardFileName = cardImagePath.substring(cardLastSlashIndex + 1);
            const cardDotIndex = cardFileName.lastIndexOf('.');
            const cardFileNameWithoutExt = cardDotIndex > 0 ? cardFileName.substring(0, cardDotIndex) : cardFileName;

            // 去掉卡片图片文件名的第一个字符'0'进行比较
            const cardFileNameWithoutZero = cardFileNameWithoutExt.startsWith('0') && cardFileNameWithoutExt.length > 1
              ? cardFileNameWithoutExt.substring(1)
              : cardFileNameWithoutExt;

            return cardFileNameWithoutZero === fileNameWithoutExt;
          });

          if (matchingCardImage) {
            goalConfig.displayCardBackgroundImage = OssService.generateResourceUrl(matchingCardImage);
          }
        }

        const tagIcon = OssService.generateResourceUrl(goalConfig.tagIcon);
        goalConfig.tagIcon = tagIcon;
      }

      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      next(error);
    }
  }

  static async bridge(req: Request, res: Response, next: NextFunction) {
    try {
      const config = await OssService.getObjectJsonContentWithCache<BridgeConfig>('bridge');
      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      next(error);
    }
  }

  static async onboarding(req: Request, res: Response, next: NextFunction) {
    try {
      const config = await OssService.getObjectJsonContentWithCache<OnboardingConfig>('onboarding');

      // 处理步骤图片
      for (const step of config.steps) {
        step.image = OssService.generateResourceUrl(step.image);
      }
      // 处理背景视频
      config.backgroundVideo = OssService.generateResourceUrl(config.backgroundVideo);
      config.startImage = OssService.generateResourceUrl(config.startImage);
      config.logoIcon = OssService.generateResourceUrl(config.logoIcon);

      config.soundSetting.rippleGif = OssService.generateResourceUrl(config.soundSetting.rippleGif);
      config.soundSetting.rippleIcon = OssService.generateResourceUrl(config.soundSetting.rippleIcon);
      // 处理音效设置中的试听图标
      config.soundSetting.auditionIcon = OssService.generateResourceUrl(config.soundSetting.auditionIcon);
      config.soundSetting.auditionPauseIcon = OssService.generateResourceUrl(config.soundSetting.auditionPauseIcon);

      config.soundSetting.leftArrowDarkIcon = OssService.generateResourceUrl(config.soundSetting.leftArrowDarkIcon);
      config.soundSetting.leftArrowIcon = OssService.generateResourceUrl(config.soundSetting.leftArrowIcon);
      config.soundSetting.rightArrowDarkIcon = OssService.generateResourceUrl(config.soundSetting.rightArrowDarkIcon);
      config.soundSetting.rightArrowIcon = OssService.generateResourceUrl(config.soundSetting.rightArrowIcon);
      config.soundSetting.playingGif = OssService.generateResourceUrl(config.soundSetting.playingGif);
      config.soundSetting.playingIcon = OssService.generateResourceUrl(config.soundSetting.playingIcon);

      // 处理每个人物的头像和音效演示
      for (const figure of config.soundSetting.figures) {
        figure.avatarImage = OssService.generateResourceUrl(figure.avatarImage);
        figure.soundDemo = OssService.generateResourceUrl(figure.soundDemo);
      }

      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      next(error);
    }
  }

  static async explore(req: Request, res: Response, next: NextFunction) {
    try {
      const config = await OssService.getObjectJsonContentWithCache<ExploreConfig>('explore');
      config.titleImage = OssService.generateResourceUrl(config.titleImage);
      for (const item of config.items) {
        item.backgroundImage = OssService.generateResourceUrl(item.backgroundImage);
      }
      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      next(error);
    }
  }

  static async tapTalk(req: Request, res: Response, next: NextFunction) {
    try {
      const config = await OssService.getObjectJsonContentWithCache<TapTalkConfig>('tap-talk');
      config.bottomIcon = OssService.generateResourceUrl(config.bottomIcon);
      config.nextIcon = OssService.generateResourceUrl(config.nextIcon);
      config.enterIcon = OssService.generateResourceUrl(config.enterIcon);

      for (const item of config.tapTalks) {
        for (const itemItem of item.items) {
          try {
            itemItem.image = OssService.generateResourceUrl(itemItem.image);
          } catch (error) {
            logger.error(`Error processing tap talk script for itemItem: ${itemItem.name}`, error);
          }
        }
      }
      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      next(error);
    }
  }

  static async divination(req: Request, res: Response, next: NextFunction) {
    try {
      const config = await OssService.getObjectJsonContentWithCache<DivinationConfig>('divination');
      config.backgroungImage = OssService.generateResourceUrl(config.backgroungImage);
      config.titleTextImage = OssService.generateResourceUrl(config.titleTextImage);
      config.guideTextImage = OssService.generateResourceUrl(config.guideTextImage);
      config.infoIcon = OssService.generateResourceUrl(config.infoIcon);
      config.loadingIcon = OssService.generateResourceUrl(config.loadingIcon);
      config.loadingBackgroundImage = OssService.generateResourceUrl(config.loadingBackgroundImage);
      config.shareHeaderImage = OssService.generateResourceUrl(config.shareHeaderImage);
      config.shareSubtitle1Image = OssService.generateResourceUrl(config.shareSubtitle1Image);
      config.shareSubtitle2Image = OssService.generateResourceUrl(config.shareSubtitle2Image);
      config.shareSubtitle3Image = OssService.generateResourceUrl(config.shareSubtitle3Image);

      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      next(error);
    }
  }

  static async pdf(req: Request, res: Response, next: NextFunction) {
    try {
      const config = await OssService.getObjectJsonContentWithCache<MyConfig>('my');

      res.json({
        success: true,
        data: {
          aboutPdfLink: config.aboutPdfLink,
          systemPdfLink: config.systemPdfLink
        }
      });
    } catch (error) {
      next(error);
    }
  }

  // Admin专用配置方法，使用签名URL绕过CDN限制
  static async adminHomepage(req: Request, res: Response, next: NextFunction) {
    try {
      const config = await OssService.getObjectJsonContentWithCache<HomepageConfig>('homepage');

      config.backgroundVideo = OssService.generateAdminResourceUrl(config.backgroundVideo);
      config.guideButtonIcon = OssService.generateAdminResourceUrl(config.guideButtonIcon);
      config.guideTextImage = OssService.generateAdminResourceUrl(config.guideTextImage);
      config.closeIcon = OssService.generateAdminResourceUrl(config.closeIcon);

      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      next(error);
    }
  }

  static async adminOnboarding(req: Request, res: Response, next: NextFunction) {
    try {
      const config = await OssService.getObjectJsonContentWithCache<OnboardingConfig>('onboarding');

      // 处理背景视频
      config.backgroundVideo = OssService.generateAdminResourceUrl(config.backgroundVideo);
      config.startImage = OssService.generateAdminResourceUrl(config.startImage);
      config.logoIcon = OssService.generateAdminResourceUrl(config.logoIcon);

      config.soundSetting.rippleGif = OssService.generateAdminResourceUrl(config.soundSetting.rippleGif);
      config.soundSetting.rippleIcon = OssService.generateAdminResourceUrl(config.soundSetting.rippleIcon);
      config.soundSetting.auditionIcon = OssService.generateAdminResourceUrl(config.soundSetting.auditionIcon);
      config.soundSetting.auditionPauseIcon = OssService.generateAdminResourceUrl(config.soundSetting.auditionPauseIcon);

      // 处理每个人物的头像和音效演示
      for (const figure of config.soundSetting.figures) {
        figure.avatarImage = OssService.generateAdminResourceUrl(figure.avatarImage);
        figure.soundDemo = OssService.generateAdminResourceUrl(figure.soundDemo);
      }

      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      next(error);
    }
  }

  static async adminExplore(req: Request, res: Response, next: NextFunction) {
    try {
      const config = await OssService.getObjectJsonContentWithCache<ExploreConfig>('explore');
      config.titleImage = OssService.generateAdminResourceUrl(config.titleImage);
      for (const item of config.items) {
        item.backgroundImage = OssService.generateAdminResourceUrl(item.backgroundImage);
      }
      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      next(error);
    }
  }

  static async adminDivination(req: Request, res: Response, next: NextFunction) {
    try {
      const config = await OssService.getObjectJsonContentWithCache<DivinationConfig>('divination');
      config.backgroungImage = OssService.generateAdminResourceUrl(config.backgroungImage);
      config.titleTextImage = OssService.generateAdminResourceUrl(config.titleTextImage);
      config.guideTextImage = OssService.generateAdminResourceUrl(config.guideTextImage);
      config.infoIcon = OssService.generateAdminResourceUrl(config.infoIcon);
      config.loadingIcon = OssService.generateAdminResourceUrl(config.loadingIcon);
      config.loadingBackgroundImage = OssService.generateAdminResourceUrl(config.loadingBackgroundImage);
      config.shareHeaderImage = OssService.generateAdminResourceUrl(config.shareHeaderImage);

      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      next(error);
    }
  }

  static async adminEndGoal(req: Request, res: Response, next: NextFunction) {
    try {
      const config = await OssService.getObjectJsonContentWithCache<EndGoalConfig>('end-goal');
      config.maskImage = OssService.generateAdminResourceUrl(config.maskImage);
      config.infoIcon = OssService.generateAdminResourceUrl(config.infoIcon);

      // 处理每个目标配置的资源
      for (const goalConfig of config.goalConfigs) {
        goalConfig.tagIcon = OssService.generateAdminResourceUrl(goalConfig.tagIcon);
      }

      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      next(error);
    }
  }

  static async adminTapTalk(req: Request, res: Response, next: NextFunction) {
    try {
      const config = await OssService.getObjectJsonContentWithCache<TapTalkConfig>('tap-talk');
      config.bottomIcon = OssService.generateAdminResourceUrl(config.bottomIcon);
      config.nextIcon = OssService.generateAdminResourceUrl(config.nextIcon);
      config.enterIcon = OssService.generateAdminResourceUrl(config.enterIcon);
      config.glichyImage = OssService.generateAdminResourceUrl(config.glichyImage);
      config.audioPlayingIcon = OssService.generateAdminResourceUrl(config.audioPlayingIcon);
      config.audioPausedIcon = OssService.generateAdminResourceUrl(config.audioPausedIcon);
      config.overlayImage = OssService.generateAdminResourceUrl(config.overlayImage);

      for (const item of config.tapTalks) {
        // 处理音频文件
        item.audios = item.audios.map(audio => OssService.generateAdminResourceUrl(audio));

        for (const itemItem of item.items) {
          try {
            itemItem.image = OssService.generateAdminResourceUrl(itemItem.image);
            itemItem.detailImage = OssService.generateAdminResourceUrl(itemItem.detailImage);
          } catch (error) {
            logger.error(`Error processing tap talk script for itemItem: ${itemItem.name}`, error);
          }
        }
      }

      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      next(error);
    }
  }
}