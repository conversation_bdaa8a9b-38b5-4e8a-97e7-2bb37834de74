import OSS from 'ali-oss';
import Cdn20180510, * as $Cdn20180510 from '@alicloud/cdn20180510';
import * as $OpenApi from '@alicloud/openapi-client';
import logger from '../utils/logger';

export class OssService {
  private static client: OSS;
  private static cdnClient: Cdn20180510;

  static {
    this.client = new OSS({
      accessKeyId: process.env.OSS_ACCESS_KEY_ID || '',
      accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET || '',
      bucket: process.env.OSS_BUCKET || '',
      secure: true
    });

    // 初始化CDN客户端
    if (process.env.OSS_ACCESS_KEY_ID && process.env.OSS_ACCESS_KEY_SECRET) {
      const config = new $OpenApi.Config({
        accessKeyId: process.env.OSS_ACCESS_KEY_ID,
        accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
        endpoint: 'cdn.aliyuncs.com'
      });
      this.cdnClient = new Cdn20180510(config);
    }
  }

  private static objectContentCache: { [key: string]: { data: string, timestamp: number } } = {};
  private static OBJECT_CONTENT_CACHE_TTL = 86400000; // 1 day


  // 添加CDN配置
  private static readonly CDN_DOMAIN = process.env.CDN_DOMAIN || ''; // 例如：https://cdn.moodplay.top
  private static readonly USE_CDN = process.env.USE_CDN === 'true';
  private static readonly AUTO_REFRESH_CDN = process.env.AUTO_REFRESH_CDN === 'true'; // 是否自动刷新CDN缓存

  /**
   * 获取OSS客户端实例
   * @returns OSS客户端实例
   */
  static getClient(): OSS {
    return this.client;
  }

  /**
   * 获取指定页面的所有资源
   * @param pageName 页面名称
   * @returns 资源对象，键为资源名，值为签名URL
   */
  static async getPageConfig(pageName: string): Promise<{ [key: string]: string }> {
    try {
      const result = await this.client.list({
        prefix: `assets/pages/${pageName}/`,
        delimiter: '/',
        "max-keys": 100,
      }, { timeout: 1000 });

      if (!result.objects || result.objects.length === 0) {
        throw new Error(`未找到${pageName}页面的资源`);
      }

      // 构建资源对象
      const assets: { [key: string]: string } = {};

      // 处理文件
      for (const obj of result.objects) {
        if (obj.name === `assets/pages/${pageName}/`) continue; // 跳过目录本身

        // 从文件名提取资源类型
        const fileName = obj.name.split('/').pop();
        if (!fileName) continue;

        const fileNameWithoutExt = fileName.split('.')[0];

        // 生成签名URL
        // const url = await this.generateSignedUrl(obj.name);

        assets[fileNameWithoutExt] = obj.name;
      }

      return assets;
    } catch (error) {
      logger.error('获取页面资源失败:', error);
      throw error;
    }
  }

  /**
   * 获取指定页面的所有资源
   * @param pageName 页面名称
   * @returns 资源对象，键为资源名，值为签名URL
   */
  static async getPageAssets(pageName: string): Promise<{ [key: string]: string }> {
    try {
      const result = await this.client.list({
        prefix: `assets/pages/${pageName}/`,
        delimiter: '/',
        "max-keys": 100,
      }, { timeout: 1000 });

      if (!result.objects || result.objects.length === 0) {
        throw new Error(`未找到${pageName}页面的资源`);
      }

      // 构建资源对象
      const assets: { [key: string]: string } = {};

      // 处理文件
      for (const obj of result.objects) {
        if (obj.name === `assets/pages/${pageName}/`) continue; // 跳过目录本身

        // 从文件名提取资源类型
        const fileName = obj.name.split('/').pop();
        if (!fileName) continue;

        const fileNameWithoutExt = fileName.split('.')[0];

        // 生成签名URL
        // const url = await this.generateSignedUrl(obj.name);

        assets[fileNameWithoutExt] = obj.name;
      }

      return assets;
    } catch (error) {
      logger.error('获取页面资源失败:', error);
      throw error;
    }
  }

  static generateUrl(objectName: string): string {
    const bucket = process.env.OSS_PUBLIC_BUCKET;
    const endpoint = process.env.OSS_ENDPOINT;
    const url = `https://${bucket}.${endpoint}/${objectName}`;
    return url;
  }

  /**
   * 生成带签名的临时访问URL。
   * @param objectName OSS对象名称/路径
   * @param expireTime URL过期时间(秒)，默认3600秒(1小时)
   * @returns Promise<string> 带签名的临时访问URL
   */
  static generateSignedUrl(objectName: string, expireTime: number = 3600): string {
    // 1. 生成新URL
    try {
      const url = this.client.signatureUrl(objectName, {
        expires: expireTime, // OSS SDK 的 expires 参数是以秒为单位的有效时长
      });

      return url; // 直接返回生成的 URL
    } catch (error) {
      logger.error(`生成签名URL失败 (${objectName}):`, error);
      throw error; // 重新抛出错误，让调用者处理
    }
  }

  /**
   * 检查文件是否为图片文件
   * @param objectName OSS对象名称/路径
   * @returns 是否为图片文件
   */
  private static isImageFile(objectName: string): boolean {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico'];
    const extension = objectName.toLowerCase().split('.').pop();
    return extension ? imageExtensions.includes(`.${extension}`) : false;
  }

  /**
   * 生成CDN URL（用于静态资源访问）
   * @param objectName OSS对象名称/路径
   * @returns CDN URL
   */
  static generateCdnUrl(objectName: string): string {
    if (!this.CDN_DOMAIN) {
      logger.warn('CDN_DOMAIN not configured, falling back to signed URL');
      return this.generateSignedUrl(objectName);
    }
    if (objectName.startsWith('/')) {
      objectName = objectName.substring(1);
    }

    let cdnUrl = `${this.CDN_DOMAIN}/${objectName}`;

    // // 为图片资源添加webp处理（小程序优化）
    // if (this.isImageFile(objectName)) {
    //   cdnUrl += '?x-oss-process=image/resize,w_1080/quality,q_85/format,webp';
    // }

    return cdnUrl;
  }

  /**
   * 生成资源URL（智能选择CDN或签名URL）
   * @param objectName OSS对象名称/路径
   * @param expireTime URL过期时间(秒)，仅在使用签名URL时有效
   * @returns 资源访问URL
   */
  static generateResourceUrl(objectName: string, expireTime: number = 3600): string {
    if (this.USE_CDN && this.CDN_DOMAIN) {
      return this.generateCdnUrl(objectName);
    } else {
      return this.generateSignedUrl(objectName, expireTime);
    }
  }

  /**
   * 为admin-frontend生成资源URL（总是使用签名URL，绕过CDN限制）
   * @param objectName OSS对象名称/路径
   * @param expireTime URL过期时间(秒)，默认24小时
   * @returns 签名URL
   */
  static generateAdminResourceUrl(objectName: string, expireTime: number = 86400): string {
    // admin-frontend总是使用签名URL，绕过CDN的User-Agent限制
    return this.generateSignedUrl(objectName, expireTime);
  }

  /**
   * 从 OSS 获取对象内容并缓存。
   * @param objectName OSS 对象名称/路径
   * @param ttl 缓存时间 (毫秒)，默认为 OBJECT_CONTENT_CACHE_TTL
   * @returns 对象内容的字符串形式
   */
  static async getObjectContentWithCache(objectName: string, ttl: number = this.OBJECT_CONTENT_CACHE_TTL): Promise<string> {
    const now = Date.now();

    // 检查缓存是否有效
    if (this.objectContentCache[objectName] &&
      now - this.objectContentCache[objectName].timestamp < ttl) {
      logger.info(`[Cache Hit] Returning cached content for OSS object: ${objectName}`);
      return this.objectContentCache[objectName].data;
    }

    logger.info(`[Cache Miss] Fetching content for OSS object: ${objectName}`);
    try {
      const signatureUrl = OssService.generateSignedUrl(objectName);
      const response = await fetch(signatureUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch ${objectName}: ${response.status} ${response.statusText}`);
      }
      const content = await response.text();

      // 更新缓存
      this.objectContentCache[objectName] = {
        data: content,
        timestamp: now
      };
      logger.info(`Successfully fetched and cached content for OSS object: ${objectName}`);
      return content;
    } catch (error) {
      logger.error(`获取 OSS 对象内容失败 (${objectName}):`, error);
      // 如果有缓存但已过期，在出错时仍返回过期缓存
      if (this.objectContentCache[objectName]) {
        logger.warn(`Returning stale cached content for OSS object due to fetch error: ${objectName}`);
        return this.objectContentCache[objectName].data;
      }
      throw error; // 重新抛出错误，让调用者处理
    }
  }

  /**
   * 从 OSS 获取配置内容，解析为 JSON 并缓存。
   * @param assetDir OSS 资源目录
   * @param defaultValue 获取或解析失败时的默认值
   * @param ttl 缓存时间 (毫秒)，默认为 OBJECT_CONTENT_CACHE_TTL
   * @returns 解析后的 JSON 对象，或在失败时返回 defaultValue
   */
  static async getObjectJsonContentWithCache<T>(
    assetDir: string,
    defaultValue?: T,
    ttl?: number
  ): Promise<T> {
    try {
      const objectName = `assets/pages/${assetDir}/config.json`;
      ttl = ttl || this.OBJECT_CONTENT_CACHE_TTL;
      const contentString = await OssService.getObjectContentWithCache(objectName, ttl);
      return JSON.parse(contentString) as T;
    } catch (error) {
      logger.error(`获取或解析 OSS JSON 对象内容失败 (${assetDir}):`, error);
      if (defaultValue) {
        return defaultValue;
      }
      throw error
    }
  }

  /**
   * 从 OSS 获取对象内容，解析为 JSON 并缓存。
   * @param assetDir OSS 资源目录
   * @param defaultValue 获取或解析失败时的默认值
   * @param ttl 缓存时间 (毫秒)，默认为 OBJECT_CONTENT_CACHE_TTL
   * @returns 解析后的 JSON 对象，或在失败时返回 defaultValue
   */
  static async getGeneralJSONWithCache(
    objectName: string,
    ttl?: number
  ): Promise<any> {
    try {
      ttl = ttl || this.OBJECT_CONTENT_CACHE_TTL;
      const contentString = await OssService.getObjectContentWithCache(objectName, ttl);
      return JSON.parse(contentString) as any;
    } catch (error) {
      logger.error(`获取或解析 OSS JSON 对象内容失败 (${objectName}):`, error);
      throw error
    }
  }

  /**
   * 刷新CDN缓存
   * @param objectName OSS对象名称/路径
   */
  static async refreshCdnCache(objectName: string): Promise<void> {
    if (!this.USE_CDN || !this.CDN_DOMAIN || !this.cdnClient) {
      logger.info(`CDN刷新跳过: USE_CDN=${this.USE_CDN}, CDN_DOMAIN=${this.CDN_DOMAIN}, cdnClient=${!!this.cdnClient}`);
      return;
    }

    try {
      // 生成完整的CDN URL
      const cdnUrl = this.generateCdnUrl(objectName);

      logger.info(`正在刷新CDN缓存: ${cdnUrl}`);

      const refreshObjectCachesRequest = new $Cdn20180510.RefreshObjectCachesRequest({
        objectPath: cdnUrl,
        objectType: 'File'
      });

      const response = await this.cdnClient.refreshObjectCaches(refreshObjectCachesRequest);

      logger.info(`CDN缓存刷新成功: ${cdnUrl}, 任务ID: ${response.body?.refreshTaskId || 'unknown'}`);
    } catch (error) {
      logger.error(`CDN缓存刷新失败 (${objectName}):`, error);
      // 不要抛出错误，避免影响主要的上传功能
    }
  }

  /**
   * 批量刷新CDN缓存
   * @param objectNames OSS对象名称数组
   */
  static async refreshCdnCacheBatch(objectNames: string[]): Promise<void> {
    if (!this.USE_CDN || !this.CDN_DOMAIN || !this.cdnClient || objectNames.length === 0) {
      return;
    }

    try {
      // 生成CDN URLs
      const cdnUrls = objectNames.map(objectName => this.generateCdnUrl(objectName));

      logger.info(`正在批量刷新CDN缓存: ${cdnUrls.join(', ')}`);

      const refreshObjectCachesRequest = new $Cdn20180510.RefreshObjectCachesRequest({
        objectPath: cdnUrls.join('\n'), // 多个URL用换行符分隔
        objectType: 'File'
      });

      const response = await this.cdnClient.refreshObjectCaches(refreshObjectCachesRequest);

      logger.info(`CDN缓存批量刷新成功，任务ID: ${response.body?.refreshTaskId || 'unknown'}`);
    } catch (error) {
      logger.error(`CDN缓存批量刷新失败:`, error);
      // 不要抛出错误，避免影响主要的上传功能
    }
  }

  /**
   * 将字符串内容上传到 OSS 对象，会覆盖现有对象。
   * @param objectName OSS 对象名称/路径
   * @param content 要上传的字符串内容
   */
  static async putObjectContent(objectName: string, content: string): Promise<void> {
    try {
      logger.info(`[OSS] Putting content to OSS object: ${objectName}`);
      // OSS put 方法需要 Buffer
      await this.client.put(objectName, Buffer.from(content));
      logger.info(`Successfully put content to OSS object: ${objectName}`);

      // 如果内容被修改，需要使现有缓存失效
      if (this.objectContentCache[objectName]) {
        delete this.objectContentCache[objectName];
        logger.info(`Cache invalidated for OSS object: ${objectName} after update.`);
      }

      // 如果启用了CDN自动刷新，则刷新CDN缓存
      if (this.AUTO_REFRESH_CDN) {
        await this.refreshCdnCache(objectName);
      }
    } catch (error) {
      logger.error(`上传内容到 OSS 对象失败 (${objectName}):`, error);
      throw error;
    }
  }

  /**
   * 将文件Buffer上传到 OSS 对象，会覆盖现有对象。
   * @param objectName OSS 对象名称/路径
   * @param buffer 要上传的文件Buffer
   * @param options 额外的上传选项，如 Content-Type
   */
  static async putObjectBuffer(objectName: string, buffer: Buffer, options?: { contentType?: string }): Promise<void> {
    try {
      logger.info(`[OSS] Putting file buffer to OSS object: ${objectName}, size: ${buffer.length} bytes`);

      const putOptions: any = {};
      if (options?.contentType) {
        putOptions.headers = {
          'Content-Type': options.contentType
        };
      }
      await this.client.put(objectName, buffer, putOptions);
      logger.info(`Successfully put file buffer to OSS object: ${objectName}`);

      // 如果内容被修改，需要使现有缓存失效
      if (this.objectContentCache[objectName]) {
        delete this.objectContentCache[objectName];
        logger.info(`Cache invalidated for OSS object: ${objectName} after update.`);
      }

      // 如果启用了CDN自动刷新，则刷新CDN缓存
      if (this.AUTO_REFRESH_CDN) {
        await this.refreshCdnCache(objectName);
      }
    } catch (error) {
      logger.error(`上传文件到 OSS 对象失败 (${objectName}):`, error);
      throw error;
    }
  }

  /**
   * 处理包含OSS URL的对象，将所有URL转换为带签名的临时URL
   * @param data 包含OSS URL的对象或数组
   * @param urlFields 需要处理的URL字段名数组
   * @returns 处理后的对象或数组
   */
  static async processOssUrls(data: any, urlFields: string[]): Promise<any> {
    if (!data) return data;

    // 处理数组
    if (Array.isArray(data)) {
      const promises = data.map(item => this.processOssUrls(item, urlFields));
      return Promise.all(promises);
    }

    // 处理对象
    if (typeof data === 'object') {
      const promises = Object.keys(data).map(async key => {
        // 如果是需要处理的URL字段
        if (urlFields.includes(key) && typeof data[key] === 'string' && data[key].includes('.aliyuncs.com')) {
          // 从完整URL中提取对象名称
          const objectName = this.extractObjectNameFromUrl(data[key]);
          if (objectName) {
            data[key] = this.generateSignedUrl(objectName);
          }
        }
        // 递归处理嵌套对象
        else if (typeof data[key] === 'object' && data[key] !== null) {
          data[key] = await this.processOssUrls(data[key], urlFields);
        }
      });

      await Promise.all(promises);
      return data;
    }

    return data;
  }

  /**
   * 从完整的OSS URL中提取对象名称
   * @param url 完整的OSS URL
   * @returns 对象名称
   */
  static extractObjectNameFromUrl(url: string): string | null {
    try {
      // 假设URL格式为 https://bucket.region.aliyuncs.com/object-name
      const urlObj = new URL(url);
      // 移除开头的斜杠
      return urlObj.pathname.startsWith('/') ? urlObj.pathname.substring(1) : urlObj.pathname;
    } catch (error) {
      logger.error('从URL提取对象名称失败:', error);
      return null;
    }
  }

  /**
   * 获取指定OSS路径下的所有对象名（完整路径）
   * @param prefix OSS路径前缀
   * @param maxKeys 最大返回数量，默认100
   * @returns Promise<string[]> 对象完整路径名数组
   */
  static async getAllObjectNames(prefix: string, maxKeys: number = 100): Promise<string[]> {
    try {
      logger.info(`获取OSS路径下的所有对象: ${prefix}`);

      const result = await this.client.list({
        prefix: prefix,
        "max-keys": maxKeys,
      }, { timeout: 5000 });

      if (!result.objects || result.objects.length === 0) {
        logger.warn(`路径 ${prefix} 下未找到任何对象`);
        return [];
      }

      // 提取所有对象的完整路径名
      const objectNames: string[] = result.objects
        .filter(obj => obj.name !== prefix) // 过滤掉目录本身
        .map(obj => obj.name);

      logger.info(`成功获取到 ${objectNames.length} 个对象，路径: ${prefix}`);
      return objectNames;
    } catch (error) {
      logger.error(`获取OSS路径下对象失败 (${prefix}):`, error);
      throw error;
    }
  }

  /**
   * 删除OSS对象
   * @param objectName OSS对象名称/路径
   */
  static async deleteObject(objectName: string): Promise<void> {
    try {
      logger.info(`[OSS] Deleting object: ${objectName}`);
      await this.client.delete(objectName);
      logger.info(`Successfully deleted OSS object: ${objectName}`);

      // 清除相关缓存
      if (this.objectContentCache[objectName]) {
        delete this.objectContentCache[objectName];
        logger.info(`Cache invalidated for deleted OSS object: ${objectName}`);
      }

      // 如果启用了CDN自动刷新，则刷新CDN缓存
      if (this.AUTO_REFRESH_CDN) {
        await this.refreshCdnCache(objectName);
      }
    } catch (error) {
      logger.error(`删除OSS对象失败 (${objectName}):`, error);
      throw error;
    }
  }

  /**
   * 批量删除OSS对象
   * @param objectNames OSS对象名称数组
   */
  static async deleteObjects(objectNames: string[]): Promise<void> {
    if (!objectNames || objectNames.length === 0) {
      logger.info('[OSS] No objects to delete');
      return;
    }

    try {
      logger.info(`[OSS] Batch deleting ${objectNames.length} objects:`, objectNames);

      // 使用OSS的批量删除功能
      const result = await this.client.deleteMulti(objectNames);

      logger.info(`Successfully deleted ${objectNames.length} OSS objects`);

      // 清除相关缓存
      objectNames.forEach(objectName => {
        if (this.objectContentCache[objectName]) {
          delete this.objectContentCache[objectName];
          logger.info(`Cache invalidated for deleted OSS object: ${objectName}`);
        }
      });

      // 如果启用了CDN自动刷新，则批量刷新CDN缓存
      if (this.AUTO_REFRESH_CDN) {
        await this.refreshCdnCacheBatch(objectNames);
      }
    } catch (error) {
      logger.error(`批量删除OSS对象失败:`, error);
      throw error;
    }
  }

  /**
   * 检查OSS对象是否存在
   * @param objectName OSS对象名称/路径
   * @returns Promise<boolean> 对象是否存在
   */
  static async objectExists(objectName: string): Promise<boolean> {
    try {
      await this.client.head(objectName);
      return true;
    } catch (error: any) {
      if (error.code === 'NoSuchKey' || error.status === 404) {
        return false;
      }
      logger.error(`检查OSS对象是否存在失败 (${objectName}):`, error);
      throw error;
    }
  }

  /**
   * 清除所有对象内容缓存
   * @returns 清除的缓存项数量
   */
  static clearAllObjectContentCache(): number {
    try {
      const cacheKeys = Object.keys(this.objectContentCache);
      const clearedCount = cacheKeys.length;

      // 清空缓存对象
      this.objectContentCache = {};

      logger.info(`[Cache Clear] Successfully cleared all object content cache. Cleared ${clearedCount} cache entries.`);

      return clearedCount;
    } catch (error) {
      logger.error('清除对象内容缓存失败:', error);
      throw error;
    }
  }

}
