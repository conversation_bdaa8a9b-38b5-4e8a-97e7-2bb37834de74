import { Router } from 'express';
import { AdminController } from '../controllers/admin.controller';
import { ConfigController } from '../controllers/config.controller';
import { authAdminMiddleware } from '../middlewares/auth.admin.middleware';
import { uploadSingle } from '../middlewares/upload.middleware';

const router = Router();

// 登录接口 - 不需要认证
router.post('/login', AdminController.login);

// 验证token - 需要认证
router.post('/verify-token', authAdminMiddleware, AdminController.verifyToken);

// 以下接口都需要认证
router.get('/config/:pageName', authAdminMiddleware, AdminController.getConfig);

router.put('/config/:pageName', authAdminMiddleware, AdminController.updateConfig);

router.post('/resource/:pageName/upload', authAdminMiddleware, uploadSingle, AdminController.updateResource);

router.post('/script-content', authAdminMiddleware, AdminController.getScriptContent);

// 系统状态管理
router.get('/system-status', authAdminMiddleware, AdminController.getSystemStatus);

router.put('/system-status', authAdminMiddleware, AdminController.updateSystemStatus);

// 批量发放用户奖励
router.post('/grant-batch-rewards', authAdminMiddleware, AdminController.grantBatchRewards);

// Admin专用配置接口，使用签名URL绕过CDN限制
router.get('/admin-config/homepage', authAdminMiddleware, ConfigController.adminHomepage);
router.get('/admin-config/onboarding', authAdminMiddleware, ConfigController.adminOnboarding);
router.get('/admin-config/explore', authAdminMiddleware, ConfigController.adminExplore);
router.get('/admin-config/divination', authAdminMiddleware, ConfigController.adminDivination);
router.get('/admin-config/end-goal', authAdminMiddleware, ConfigController.adminEndGoal);
router.get('/admin-config/tap-talk', authAdminMiddleware, ConfigController.adminTapTalk);

export default router;