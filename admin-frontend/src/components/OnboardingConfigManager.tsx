import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { Button } from './ui/button'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from './ui/accordion'
import { Upload, ArrowLeft, X, Music, Image, FileText, Loader2, Sparkles, Settings, Database, Play, Pause, Eye, Volume2, Edit, Save, Video } from 'lucide-react'
import { mockPages, mockOnboardingConfig } from '../lib/utils'
import { apiService } from '../services/api'
import { getFileName } from '../utils/url'
import type { Page } from '../types/config'
import type { OnboardingConfig } from '../types/onboarding'

// 图片预览弹窗组件
function ImagePreviewModal({ src, alt, isOpen, onClose }: { src: string; alt: string; isOpen: boolean; onClose: () => void }) {
  if (!isOpen) return null

  return (
    <div 
      className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <div 
        className="relative max-w-4xl max-h-full bg-white rounded-2xl shadow-2xl overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="absolute top-4 right-4 z-10">
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="bg-white/90 hover:bg-white rounded-full shadow-lg"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>
        <img 
          src={src} 
          alt={alt}
          className="w-full h-full object-contain max-h-[80vh]"
          onError={(e) => {
            (e.target as HTMLImageElement).src = '/placeholder-image.png'
          }}
        />
      </div>
    </div>
  )
}

// 音频播放器组件
function AudioPlayer({ src, fileName }: { src: string; fileName: string }) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [audio, setAudio] = useState<HTMLAudioElement | null>(null)

  const togglePlay = () => {
    if (!audio) {
      const newAudio = new Audio(src)
      newAudio.addEventListener('ended', () => setIsPlaying(false))
      newAudio.addEventListener('error', () => {
        console.error('音频加载失败:', src)
        setIsPlaying(false)
      })
      setAudio(newAudio)
      newAudio.play().then(() => setIsPlaying(true)).catch(() => setIsPlaying(false))
    } else {
      if (isPlaying) {
        audio.pause()
        setIsPlaying(false)
      } else {
        audio.play().then(() => setIsPlaying(true)).catch(() => setIsPlaying(false))
      }
    }
  }

  return (
    <div className="flex items-center justify-between bg-white rounded-lg p-3 border border-blue-200 group hover:shadow-md transition-all duration-200">
      <div className="flex items-center flex-1 min-w-0 mr-3">
        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3 shadow-sm">
          <Volume2 className="w-4 h-4 text-white" />
        </div>
        <div className="flex-1 min-w-0">
          <p 
            className="text-sm font-medium text-gray-900 truncate group-hover:text-blue-600 transition-colors duration-200 cursor-default" 
            title={fileName}
          >
            {fileName}
          </p>
        </div>
      </div>
      <Button
        size="sm"
        onClick={togglePlay}
        variant="ghost"
        className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg shadow-sm flex-shrink-0"
      >
        {isPlaying ? (
          <Pause className="h-4 w-4" />
        ) : (
          <Play className="h-4 w-4" />
        )}
      </Button>
    </div>
  )
}

// 视频播放器组件
function VideoPlayer({ src, fileName }: { src: string; fileName: string }) {
  const [showPlayer, setShowPlayer] = useState(false)

  if (showPlayer) {
    return (
      <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4" onClick={() => setShowPlayer(false)}>
        <div className="relative max-w-4xl max-h-full bg-white rounded-2xl shadow-2xl overflow-hidden" onClick={(e) => e.stopPropagation()}>
          <div className="absolute top-4 right-4 z-10">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowPlayer(false)}
              className="bg-white/90 hover:bg-white rounded-full shadow-lg"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
          <video 
            src={src} 
            controls 
            autoPlay
            className="w-full h-full object-contain max-h-[80vh]"
            onError={() => {
              console.error('视频加载失败:', src)
            }}
          />
        </div>
      </div>
    )
  }

  return (
    <div className="flex items-center justify-between bg-white rounded-lg p-3 border border-purple-200 group hover:shadow-md transition-all duration-200">
      <div className="flex items-center flex-1 min-w-0 mr-3">
        <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mr-3 shadow-sm">
          <Video className="w-4 h-4 text-white" />
        </div>
        <div className="flex-1 min-w-0">
          <p 
            className="text-sm font-medium text-gray-900 truncate group-hover:text-purple-600 transition-colors duration-200 cursor-default" 
            title={fileName}
          >
            {fileName}
          </p>
        </div>
      </div>
      <Button
        size="sm"
        onClick={() => setShowPlayer(true)}
        variant="ghost"
        className="text-purple-600 hover:text-purple-700 hover:bg-purple-50 rounded-lg shadow-sm flex-shrink-0"
      >
        <Play className="h-4 w-4" />
      </Button>
    </div>
  )
}

// 图片预览组件
function ImagePreview({ src, fileName, type }: { src: string; fileName: string; type?: string }) {
  const [showPreview, setShowPreview] = useState(false)
  
  const getBgColorByType = (type?: string) => {
    switch (type) {
      case 'avatar': return 'from-green-50 to-emerald-50 border-green-200'
      default: return 'from-gray-50 to-slate-50 border-gray-200'
    }
  }

  const getIconColorByType = (type?: string) => {
    switch (type) {
      case 'avatar': return 'from-green-500 to-emerald-600'
      default: return 'from-gray-500 to-slate-600'
    }
  }

  return (
    <>
      <div className={`bg-gradient-to-br ${getBgColorByType(type)} rounded-lg p-3 border group hover:shadow-md transition-all duration-200 cursor-pointer`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center flex-1 min-w-0 mr-3">
            <div className={`w-8 h-8 bg-gradient-to-br ${getIconColorByType(type)} rounded-lg flex items-center justify-center mr-3 shadow-sm`}>
              <Image className="w-4 h-4 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <p 
                className="text-sm font-medium text-gray-900 truncate group-hover:text-blue-600 transition-colors duration-200" 
                title={fileName}
              >
                {fileName}
              </p>
            </div>
          </div>
          <Button
            size="sm"
            onClick={() => setShowPreview(true)}
            variant="ghost"
            className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg shadow-sm flex-shrink-0"
          >
            <Eye className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <ImagePreviewModal 
        src={src}
        alt={fileName}
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
      />
    </>
  )
}

interface OnboardingConfigManagerProps {
  onBackToPages: () => void
}

export function OnboardingConfigManager({ onBackToPages }: OnboardingConfigManagerProps) {
  const [config, setConfig] = useState<OnboardingConfig | null>(null)
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState<string | null>(null) // 正在上传的资源ID
  const [error, setError] = useState<string | null>(null)
  
  // 文本编辑相关状态
  const [editingText, setEditingText] = useState<{
    figureId: number
    originalText: string
    newText: string
  } | null>(null)
  const [savingText, setSavingText] = useState(false)

  // 获取CDN基础URL
  const getCdnBaseUrl = () => {
    // 可以根据环境变量或其他方式判断环境
    const hostname = window.location.hostname
    if (hostname === 'localhost' || hostname.includes('test') || hostname.includes('dev')) {
      return 'https://test-cdn.moodplay.top'
    }
    return 'https://cdn.moodplay.top'
  }

  // 构建完整的资源URL
  const getResourceUrl = (path: string) => {
    if (path.startsWith('http')) {
      return path // 已经是完整URL
    }
    return `${getCdnBaseUrl()}/${path.replace(/^\//, '')}`
  }

  // 组件加载时获取onboarding页面配置
  useEffect(() => {
    const loadOnboardingConfig = async () => {
      setError(null)
      setLoading(true)
      
      try {
        // 从API获取配置（使用admin专用接口，绕过CDN限制）
        const response = await apiService.getAdminPageConfig('onboarding')
        console.log("response.data:", response.data)
        if (response.data) {
          // 检查response.data是否已经是对象
          const configData = typeof response.data === 'string' 
            ? JSON.parse(response.data) 
            : response.data
          setConfig(configData)
        } else {
          console.error('获取配置失败:', )
          throw new Error(response.message || '获取配置失败')
        }
      } catch (err) {
        console.error('获取配置失败:', err)
        setError(err instanceof Error ? err.message : '获取配置失败')
        
        // 使用mock数据作为fallback
        setConfig(mockOnboardingConfig)
      } finally {
        setLoading(false)
      }
    }

    loadOnboardingConfig()
  }, [])

  const handleFileUpload = async (figureId: number | null, field: string, fileType: string) => {
    if (!config) return

    const input = document.createElement('input')
    input.type = 'file'
    input.accept = getFileAccept(fileType)
    
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file || !config) return

      const resourceId = figureId ? `figure-${figureId}-${field}` : `global-${field}`
      setUploading(resourceId)
      setError(null)

      try {
        // 1. 更新配置对象中的文件路径
        const newFileName = file.name
        const newPath = figureId 
          ? `assets/pages/onboarding/sound-setting/${newFileName}`
          : `assets/pages/onboarding/${newFileName}`
        
        const updatedConfig = { ...config }
        
        if (figureId) {
          // 更新figure中的资源
          const figure = updatedConfig.soundSetting.figures.find(f => f.id === figureId)
          if (figure) {
            (figure as any)[field] = newPath
          }
        } else {
          // 更新全局资源
          (updatedConfig as any)[field] = newPath
        }

        // 2. 调用API上传文件并更新配置
        const response = await apiService.uploadResourceAndUpdateConfig(
          'onboarding',
          file,
          updatedConfig
        )

        if (response && response.success) {
          // 3. 更新本地状态
          setConfig(updatedConfig)
          console.log('文件上传成功:', response.data)
        } else {
          throw new Error(response?.message || '上传失败')
        }
      } catch (err) {
        console.error('文件上传失败:', err)
        setError(err instanceof Error ? err.message : '文件上传失败')
      } finally {
        setUploading(null)
      }
    }
    
    input.click()
  }

  // 开始编辑介绍文本
  const handleStartEditText = (figureId: number, currentText: string) => {
    setEditingText({
      figureId,
      originalText: currentText,
      newText: currentText
    })
  }

  // 保存文本修改
  const handleSaveText = async () => {
    if (!config || !editingText) return

    if (editingText.newText.trim() === '') {
      alert('介绍文本不能为空')
      return
    }

    if (editingText.newText === editingText.originalText) {
      // 文本没有变化，直接取消编辑
      setEditingText(null)
      return
    }

    setSavingText(true)
    setError(null)

    try {
      // 更新配置对象中的文本
      const updatedConfig = { ...config }
      const figure = updatedConfig.soundSetting.figures.find(f => f.id === editingText.figureId)
      if (figure) {
        figure.introduce1 = editingText.newText.trim()
      }

      // 调用API更新配置
      const response = await apiService.updatePageConfig('onboarding', updatedConfig)
      
      if (response && response.success) {
        // 更新本地状态
        setConfig(updatedConfig)
        setEditingText(null)
        console.log('文本修改成功')
      } else {
        throw new Error(response?.message || '保存失败')
      }
    } catch (err) {
      console.error('文本修改失败:', err)
      setError(err instanceof Error ? err.message : '文本修改失败')
    } finally {
      setSavingText(false)
    }
  }

  // 取消编辑文本
  const handleCancelEditText = () => {
    setEditingText(null)
  }

  const getFileAccept = (fileType: string) => {
    switch (fileType) {
      case 'image':
        return 'image/*'
      case 'audio':
        return 'audio/*'
      case 'video':
        return 'video/*'
      default:
        return '*/*'
    }
  }





  if (!config) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <div className="relative z-10 p-8">
          <div className="max-w-6xl mx-auto">
            {/* 导航栏 */}
            <div className="flex items-center mb-8">
                          <Button 
              variant="ghost" 
              onClick={onBackToPages} 
              className="mr-6 h-12 px-6 rounded-xl hover:bg-white/80 hover:shadow-lg transition-all duration-200"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              <span className="font-medium">返回首页</span>
            </Button>
            <div className="flex items-center">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                <span className="text-white text-xl">🎬</span>
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  Onboarding 页面 配置管理
                </h1>
              </div>
            </div>
            </div>

            {/* 状态卡片 */}
            <Card className="backdrop-blur-sm bg-white/80 shadow-xl border-0 rounded-2xl">
              <CardContent className="text-center py-16">
                {loading ? (
                  <div className="space-y-6">
                    <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto shadow-xl">
                      <Loader2 className="h-10 w-10 animate-spin text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">正在加载配置数据</h3>
                      <p className="text-gray-600">请稍候，正在获取页面配置信息...</p>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-6">
                    <div className="w-20 h-20 bg-gradient-to-br from-gray-400 to-gray-500 rounded-2xl flex items-center justify-center mx-auto shadow-xl">
                      <Database className="h-10 w-10 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">暂无配置数据</h3>
                      <p className="text-gray-600 mb-4">该页面暂时没有可用的配置数据</p>
                      {error && (
                        <div className="bg-red-50 border border-red-200 rounded-xl p-4 max-w-md mx-auto">
                          <p className="text-red-700 font-medium">⚠️ {error}</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 装饰性背景 */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KPGcgZmlsbD0iIzAwMDAwMCIgZmlsbC1vcGFjaXR5PSIwLjAzIj4KPGNpcmNsZSBjeD0iMzAiIGN5PSIzMCIgcj0iMiIvPgo8L2c+CjwvZz4KPC9zdmc+')] opacity-40"></div>
      
      <div className="relative z-10 p-8">
        <div className="max-w-7xl mx-auto">
          {/* 头部导航 */}
          <div className="flex items-center mb-8">
            <Button 
              variant="ghost" 
              onClick={onBackToPages} 
              className="mr-6 h-12 px-6 rounded-xl hover:bg-white/80 hover:shadow-lg transition-all duration-200 border border-white/50"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              <span className="font-medium">返回首页</span>
            </Button>
            <div className="flex items-center">
              <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-4 shadow-xl">
                <span className="text-white text-2xl">🎬</span>
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-900 bg-clip-text text-transparent">
                  Onboarding 内容配置管理
                </h1>
              </div>
            </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="mb-8">
              <Card className="border-red-200 bg-gradient-to-r from-red-50 to-pink-50 shadow-lg rounded-2xl overflow-hidden">
                <CardContent className="py-6">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-red-500 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                      <span className="text-white text-xl">⚠️</span>
                    </div>
                    <div>
                      <p className="text-red-800 font-medium text-lg">发生错误</p>
                      <p className="text-red-700 mt-1">{error}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* 主要配置区域 */}
          <div className="space-y-8">
            {/* 背景视频配置 */}
            <Card className="backdrop-blur-sm bg-white/90 shadow-2xl border-0 rounded-3xl overflow-hidden">
              <div className="bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 p-1">
                <div className="bg-white rounded-t-[calc(1.5rem-1px)]">
                  <CardHeader className="text-center pb-6 pt-6">
                    <div className="flex items-center justify-center mb-4">
                      <div className="w-14 h-14 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-xl">
                        <Video className="w-7 h-7 text-white" />
                      </div>
                    </div>
                    <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
                      背景视频管理
                    </CardTitle>
                    <CardDescription className="text-gray-600 text-base">
                      管理onboarding的背景视频文件
                    </CardDescription>
                  </CardHeader>
                </div>
              </div>
              
              <CardContent className="p-8 bg-white rounded-b-3xl">
                <div className="space-y-6">
                  <VideoPlayer 
                    src={getResourceUrl(config.backgroundVideo)}
                    fileName={getFileName(config.backgroundVideo)}
                  />
                  <div className="flex justify-center">
                    <Button
                      onClick={() => handleFileUpload(null, 'backgroundVideo', 'video')}
                      className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white shadow-lg rounded-xl px-8 py-3 transition-all duration-200 hover:shadow-xl"
                      disabled={uploading === 'global-backgroundVideo'}
                    >
                      {uploading === 'global-backgroundVideo' ? (
                        <Loader2 className="h-5 w-5 animate-spin mr-2" />
                      ) : (
                        <Upload className="h-5 w-5 mr-2" />
                      )}
                      更换背景视频
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 声音设置人物配置 */}
            <Card className="backdrop-blur-sm bg-white/90 shadow-2xl border-0 rounded-3xl overflow-hidden">
              <div className="bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 p-1">
                <div className="bg-white rounded-t-[calc(1.5rem-1px)]">
                  <CardHeader className="text-center pb-6 pt-6">
                    <div className="flex items-center justify-center mb-4">
                      <div className="relative">
                        <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-xl">
                          <Sparkles className="w-7 h-7 text-white" />
                        </div>
                        <div className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-br from-pink-500 to-rose-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs font-bold">{config.soundSetting.figures.length}</span>
                        </div>
                      </div>
                    </div>
                    <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
                      声音人物配置
                    </CardTitle>
                    <CardDescription className="text-gray-600 text-base">
                      管理声音设置相关配置
                    </CardDescription>
                  </CardHeader>
                </div>
              </div>
              
              <CardContent className="p-8 bg-white rounded-b-3xl">
                <Accordion type="multiple" className="w-full space-y-6">
                  {config.soundSetting.figures.map((figure) => (
                    <AccordionItem key={figure.id} value={figure.id.toString()} className="border-0 shadow-lg rounded-2xl overflow-hidden">
                      <AccordionTrigger className="text-left p-6 bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 border border-blue-200 hover:no-underline transition-all duration-300 hover:shadow-xl">
                        <div className="flex items-center justify-between w-full mr-4">
                          <div className="flex items-center">
                            <div className="w-14 h-14 bg-white/80 backdrop-blur-sm rounded-2xl flex items-center justify-center mr-6 shadow-lg">
                              <span className="text-2xl">🎤</span>
                            </div>
                            <div>
                              <h3 className="text-xl font-bold text-gray-900 mb-1">{figure.name}</h3>
                            </div>
                          </div>
                        </div>
                      </AccordionTrigger>
                      
                      <AccordionContent className="bg-white">
                        <div className="p-8 space-y-8">
                          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                            {/* 头像图片 */}
                            <div className="space-y-4">
                              <div className="flex items-center">
                                <Image className="w-5 h-5 mr-3 text-green-600" />
                                <span className="font-semibold text-gray-800">头像图片</span>
                              </div>
                              <ImagePreview 
                                src={getResourceUrl(figure.avatarImage)}
                                fileName={getFileName(figure.avatarImage)}
                                type="avatar"
                              />
                              <Button
                                size="sm"
                                onClick={() => handleFileUpload(figure.id, 'avatarImage', 'image')}
                                className="w-full bg-green-500 hover:bg-green-600 text-white rounded-lg shadow-md transition-all duration-200"
                                disabled={uploading === `figure-${figure.id}-avatarImage`}
                              >
                                {uploading === `figure-${figure.id}-avatarImage` ? (
                                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                ) : (
                                  <Upload className="h-4 w-4 mr-2" />
                                )}
                                更换头像
                              </Button>
                            </div>

                            {/* 声音演示 */}
                            <div className="space-y-4">
                              <div className="flex items-center">
                                <Music className="w-5 h-5 mr-3 text-blue-600" />
                                <span className="font-semibold text-gray-800">声音演示</span>
                              </div>
                              <AudioPlayer 
                                src={getResourceUrl(figure.soundDemo)}
                                fileName={getFileName(figure.soundDemo)}
                              />
                              <Button
                                size="sm"
                                onClick={() => handleFileUpload(figure.id, 'soundDemo', 'audio')}
                                className="w-full bg-blue-500 hover:bg-blue-600 text-white rounded-lg shadow-md transition-all duration-200"
                                disabled={uploading === `figure-${figure.id}-soundDemo`}
                              >
                                {uploading === `figure-${figure.id}-soundDemo` ? (
                                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                ) : (
                                  <Upload className="h-4 w-4 mr-2" />
                                )}
                                更换音频
                              </Button>
                            </div>

                            {/* 介绍文本 */}
                            <div className="space-y-4">
                              <div className="flex items-center">
                                <FileText className="w-5 h-5 mr-3 text-purple-600" />
                                <span className="font-semibold text-gray-800">介绍文本</span>
                              </div>
                              <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-200 min-h-[120px]">
                                {editingText?.figureId === figure.id ? (
                                  // 编辑状态
                                  <div className="space-y-3">
                                    <textarea
                                      value={editingText.newText}
                                      onChange={(e) => setEditingText({ ...editingText, newText: e.target.value })}
                                      className="w-full h-24 p-3 text-sm text-gray-900 bg-white border border-purple-300 rounded-lg focus:outline-none focus:border-purple-500 resize-none"
                                      placeholder="请输入介绍文本"
                                      autoFocus
                                    />
                                    <div className="flex justify-end space-x-2">
                                      <Button
                                        size="sm"
                                        onClick={handleSaveText}
                                        disabled={savingText}
                                        className="bg-green-500 hover:bg-green-600 text-white rounded-lg shadow-md"
                                      >
                                        {savingText ? (
                                          <Loader2 className="h-4 w-4 animate-spin" />
                                        ) : (
                                          <Save className="h-4 w-4" />
                                        )}
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="ghost"
                                        onClick={handleCancelEditText}
                                        disabled={savingText}
                                        className="text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg"
                                      >
                                        <X className="h-4 w-4" />
                                      </Button>
                                    </div>
                                  </div>
                                ) : (
                                  // 显示状态
                                  <div className="space-y-3">
                                    <p className="text-sm text-gray-900 whitespace-pre-wrap leading-relaxed">
                                      {figure.introduce1}
                                    </p>
                                    <div className="flex justify-end">
                                      <Button
                                        size="sm"
                                        variant="ghost"
                                        onClick={() => handleStartEditText(figure.id, figure.introduce1)}
                                        className="text-purple-600 hover:text-purple-700 hover:bg-purple-50 rounded-lg shadow-sm"
                                        disabled={savingText}
                                      >
                                        <Edit className="h-4 w-4" />
                                      </Button>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
} 