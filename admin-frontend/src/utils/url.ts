/**
 * 从URL或路径中提取文件名
 * 支持完整的签名URL和相对路径，自动解码URL编码
 * @param path URL或路径
 * @returns 解码后的文件名（不包含查询参数）
 */
export function getFileName(path: string): string {
  try {
    let fileName = '';

    // 如果是完整URL，先解析URL获取pathname
    if (path.startsWith('http')) {
      const url = new URL(path)
      const pathname = url.pathname
      const parts = pathname.split('/')
      fileName = parts[parts.length - 1]
    } else {
      // 如果是相对路径，直接分割
      const parts = path.split('/')
      fileName = parts[parts.length - 1]
    }

    // 解码URL编码的文件名（如 %20 -> 空格）
    try {
      fileName = decodeURIComponent(fileName)
    } catch (decodeError) {
      // 如果解码失败，使用原始文件名
      console.warn('Failed to decode filename:', fileName, decodeError)
    }

    return fileName
  } catch (error) {
    // 如果URL解析失败，回退到简单分割
    const parts = path.split('/')
    let fileName = parts[parts.length - 1].split('?')[0] // 移除查询参数

    // 尝试解码
    try {
      fileName = decodeURIComponent(fileName)
    } catch (decodeError) {
      // 解码失败时保持原样
    }

    return fileName
  }
}

/**
 * 从URL中提取不带扩展名的文件名
 * @param path URL或路径
 * @returns 解码后的不带扩展名的文件名
 */
export function getFileNameWithoutExtension(path: string): string {
  const fileName = getFileName(path)
  const lastDotIndex = fileName.lastIndexOf('.')
  return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName
}

/**
 * 从URL中提取文件扩展名
 * @param path URL或路径
 * @returns 文件扩展名（包含点号）
 */
export function getFileExtension(path: string): string {
  const fileName = getFileName(path)
  const lastDotIndex = fileName.lastIndexOf('.')
  return lastDotIndex > 0 ? fileName.substring(lastDotIndex) : ''
}
