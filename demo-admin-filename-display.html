<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin 文件名显示演示</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .demo-section { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .resource-item { display: flex; align-items: center; padding: 12px; margin: 8px 0; background: #f8f9fa; border-radius: 6px; border-left: 4px solid #007bff; }
        .resource-icon { width: 32px; height: 32px; background: linear-gradient(135deg, #007bff, #0056b3); border-radius: 6px; display: flex; align-items: center; justify-content: center; margin-right: 12px; color: white; font-weight: bold; }
        .resource-info { flex: 1; }
        .resource-name { font-weight: 600; color: #333; margin-bottom: 4px; }
        .resource-url { font-size: 12px; color: #666; word-break: break-all; }
        .success { color: #28a745; }
        .login-section { background: #e3f2fd; border-left: 4px solid #2196f3; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .loading { color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 Admin CDN绕过 + 文件名显示优化演示</h1>
        
        <div class="demo-section login-section">
            <h3>1. 登录管理员账户</h3>
            <button onclick="login()" id="loginBtn">登录</button>
            <div id="loginStatus"></div>
        </div>

        <div class="demo-section">
            <h3>2. 加载配置并展示文件名优化效果</h3>
            <button onclick="loadConfigs()" id="loadBtn" disabled>加载所有配置</button>
            <div id="configResults"></div>
        </div>

        <div class="demo-section">
            <h3>3. 对比效果</h3>
            <p><strong>✅ 优化后：</strong>显示简洁的文件名，如 <code>audio-paused.svg</code></p>
            <p><strong>❌ 优化前：</strong>显示完整签名URL，如 <code>https://moodplay-test.oss-cn-hangzhou.aliyuncs.com/assets/pages/tap-talk/audio-paused.svg?OSSAccessKeyId=...&Expires=...&Signature=...</code></p>
        </div>
    </div>

    <script>
        let adminToken = '';
        const API_BASE = 'http://localhost:3000/api';

        // 文件名提取函数（与前端组件中的相同）
        function getFileName(path) {
            try {
                if (path.startsWith('http')) {
                    const url = new URL(path)
                    const pathname = url.pathname
                    const parts = pathname.split('/')
                    return parts[parts.length - 1]
                }
                const parts = path.split('/')
                return parts[parts.length - 1]
            } catch (error) {
                const parts = path.split('/')
                return parts[parts.length - 1].split('?')[0]
            }
        }

        async function login() {
            const loginBtn = document.getElementById('loginBtn');
            const loginStatus = document.getElementById('loginStatus');
            
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            
            try {
                const response = await fetch(`${API_BASE}/admin/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'm$i4un5ggi9#4Dg3'
                    })
                });
                
                const data = await response.json();
                if (data.code === 0) {
                    adminToken = data.data.token;
                    loginStatus.innerHTML = `<div class="success">✅ 登录成功！</div>`;
                    document.getElementById('loadBtn').disabled = false;
                } else {
                    loginStatus.innerHTML = `<div style="color: red;">❌ 登录失败: ${data.msg}</div>`;
                }
            } catch (error) {
                loginStatus.innerHTML = `<div style="color: red;">❌ 登录错误: ${error.message}</div>`;
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            }
        }

        async function loadConfigs() {
            const loadBtn = document.getElementById('loadBtn');
            const resultsDiv = document.getElementById('configResults');
            
            loadBtn.disabled = true;
            loadBtn.textContent = '加载中...';
            resultsDiv.innerHTML = '<div class="loading">正在加载配置...</div>';
            
            const endpoints = ['homepage', 'onboarding', 'end-goal', 'tap-talk'];
            let allResources = [];
            
            try {
                for (const endpoint of endpoints) {
                    const response = await fetch(`${API_BASE}/admin/admin-config/${endpoint}`, {
                        headers: { 'Authorization': `Bearer ${adminToken}` }
                    });
                    
                    const data = await response.json();
                    if (data.code === 0) {
                        const resources = extractResources(data.data, endpoint);
                        allResources = allResources.concat(resources);
                    }
                }
                
                displayResources(allResources);
                
            } catch (error) {
                resultsDiv.innerHTML = `<div style="color: red;">❌ 加载失败: ${error.message}</div>`;
            } finally {
                loadBtn.disabled = false;
                loadBtn.textContent = '重新加载';
            }
        }

        function extractResources(data, endpoint) {
            const resources = [];
            
            function findUrls(obj, path = '', category = endpoint) {
                for (const [key, value] of Object.entries(obj)) {
                    const currentPath = path ? `${path}.${key}` : key;
                    if (typeof value === 'string' && value.includes('aliyuncs.com')) {
                        resources.push({
                            category,
                            field: currentPath,
                            url: value,
                            fileName: getFileName(value)
                        });
                    } else if (typeof value === 'object' && value !== null) {
                        findUrls(value, currentPath, category);
                    }
                }
            }
            
            findUrls(data);
            return resources;
        }

        function displayResources(resources) {
            const resultsDiv = document.getElementById('configResults');
            
            if (resources.length === 0) {
                resultsDiv.innerHTML = '<div>没有找到资源文件</div>';
                return;
            }
            
            const groupedResources = resources.reduce((groups, resource) => {
                if (!groups[resource.category]) {
                    groups[resource.category] = [];
                }
                groups[resource.category].push(resource);
                return groups;
            }, {});
            
            let html = `<h4>找到 ${resources.length} 个资源文件，全部使用签名URL绕过CDN限制：</h4>`;
            
            for (const [category, categoryResources] of Object.entries(groupedResources)) {
                html += `<h5>${category.toUpperCase()} (${categoryResources.length} 个文件)</h5>`;
                
                categoryResources.slice(0, 5).forEach(resource => {
                    const fileType = resource.fileName.split('.').pop().toLowerCase();
                    const icon = fileType === 'svg' || fileType === 'png' || fileType === 'jpg' || fileType === 'jpeg' ? '🖼️' : 
                                fileType === 'mp3' || fileType === 'wav' ? '🎵' : 
                                fileType === 'mp4' ? '🎬' : '📄';
                    
                    html += `
                        <div class="resource-item">
                            <div class="resource-icon">${icon}</div>
                            <div class="resource-info">
                                <div class="resource-name">${resource.fileName}</div>
                                <div class="resource-url">完整URL: ${resource.url}</div>
                            </div>
                        </div>
                    `;
                });
                
                if (categoryResources.length > 5) {
                    html += `<div style="color: #666; font-style: italic; margin: 10px 0;">...还有 ${categoryResources.length - 5} 个文件</div>`;
                }
            }
            
            resultsDiv.innerHTML = html;
        }
    </script>
</body>
</html>
